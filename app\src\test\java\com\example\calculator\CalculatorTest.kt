package com.example.calculator

import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for calculator operations
 */
class CalculatorTest {

    @Test
    fun addition_isCorrect() {
        assertEquals(4.0, 2.0 + 2.0, 0.0)
    }

    @Test
    fun subtraction_isCorrect() {
        assertEquals(0.0, 2.0 - 2.0, 0.0)
    }

    @Test
    fun multiplication_isCorrect() {
        assertEquals(4.0, 2.0 * 2.0, 0.0)
    }

    @Test
    fun division_isCorrect() {
        assertEquals(2.0, 4.0 / 2.0, 0.0)
    }

    @Test
    fun division_byZero_returnsNaN() {
        assertTrue(Double.isNaN(4.0 / 0.0))
    }

    @Test
    fun decimal_operations() {
        assertEquals(0.3, 0.1 + 0.2, 0.01)
    }

    @Test
    fun negative_numbers() {
        assertEquals(-5.0, -2.0 + (-3.0), 0.0)
    }

    @Test
    fun percentage_calculation() {
        assertEquals(0.5, 50.0 / 100.0, 0.0)
    }
}
