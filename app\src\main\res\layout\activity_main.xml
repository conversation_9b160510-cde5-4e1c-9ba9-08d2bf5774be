<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/calculator_background"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- Display Area -->
    <TextView
        android:id="@+id/tvDisplay"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/display_background"
        android:gravity="center_vertical|end"
        android:padding="24dp"
        android:text="@string/display_default"
        android:textColor="@color/display_text"
        android:textSize="48sp"
        android:fontFamily="monospace"
        android:maxLines="2"
        android:ellipsize="start" />

    <!-- Button Grid -->
    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="3"
        android:layout_marginTop="16dp"
        android:columnCount="4"
        android:rowCount="5"
        android:alignmentMode="alignBounds"
        android:useDefaultMargins="true">

        <!-- Row 1: Clear, Backspace, Empty, Divide -->
        <Button
            android:id="@+id/btnClear"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_function"
            android:text="@string/btn_clear"
            android:textColor="@color/function_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnBackspace"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_function"
            android:text="@string/btn_backspace"
            android:textColor="@color/function_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1" />

        <Button
            android:id="@+id/btnDivide"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_operator"
            android:text="@string/btn_divide"
            android:textColor="@color/operator_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <!-- Row 2: 7, 8, 9, Multiply -->
        <Button
            android:id="@+id/btn7"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_number"
            android:text="@string/btn_7"
            android:textColor="@color/button_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn8"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_number"
            android:text="@string/btn_8"
            android:textColor="@color/button_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn9"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_number"
            android:text="@string/btn_9"
            android:textColor="@color/button_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnMultiply"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_operator"
            android:text="@string/btn_multiply"
            android:textColor="@color/operator_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <!-- Row 3: 4, 5, 6, Minus -->
        <Button
            android:id="@+id/btn4"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_number"
            android:text="@string/btn_4"
            android:textColor="@color/button_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn5"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_number"
            android:text="@string/btn_5"
            android:textColor="@color/button_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn6"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_number"
            android:text="@string/btn_6"
            android:textColor="@color/button_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnMinus"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_operator"
            android:text="@string/btn_minus"
            android:textColor="@color/operator_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <!-- Row 4: 1, 2, 3, Plus -->
        <Button
            android:id="@+id/btn1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_number"
            android:text="@string/btn_1"
            android:textColor="@color/button_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_number"
            android:text="@string/btn_2"
            android:textColor="@color/button_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn3"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_number"
            android:text="@string/btn_3"
            android:textColor="@color/button_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnPlus"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_operator"
            android:text="@string/btn_plus"
            android:textColor="@color/operator_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <!-- Row 5: 0 (spans 2 columns), Decimal, Equals -->
        <Button
            android:id="@+id/btn0"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnSpan="2"
            android:layout_columnWeight="2"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_number"
            android:text="@string/btn_0"
            android:textColor="@color/button_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnDecimal"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_number"
            android:text="@string/btn_decimal"
            android:textColor="@color/button_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnEquals"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_columnWeight="1"
            android:layout_rowWeight="1"
            android:layout_margin="4dp"
            android:background="@drawable/button_operator"
            android:text="@string/btn_equals"
            android:textColor="@color/operator_text"
            android:textSize="24sp"
            android:textStyle="bold" />

    </GridLayout>

</LinearLayout>
