package com.example.calculator

import android.os.Bundle
import android.view.KeyEvent
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import java.text.DecimalFormat

class MainActivity : AppCompatActivity() {

    private lateinit var display: TextView
    private var currentInput = ""
    private var operator = ""
    private var firstOperand = 0.0
    private var waitingForOperand = false
    private val decimalFormat = DecimalFormat("#.##########")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        display = findViewById(R.id.tvDisplay)
        setupButtons()
    }

    private fun setupButtons() {
        // Number buttons
        val numberButtons = arrayOf(
            R.id.btn0, R.id.btn1, R.id.btn2, R.id.btn3, R.id.btn4,
            R.id.btn5, R.id.btn6, R.id.btn7, R.id.btn8, R.id.btn9
        )

        numberButtons.forEachIndexed { index, buttonId ->
            findViewById<Button>(buttonId).setOnClickListener {
                onNumberClick(index.toString())
            }
        }

        // Operator buttons
        findViewById<Button>(R.id.btnPlus).setOnClickListener { onOperatorClick("+") }
        findViewById<Button>(R.id.btnMinus).setOnClickListener { onOperatorClick("-") }
        findViewById<Button>(R.id.btnMultiply).setOnClickListener { onOperatorClick("×") }
        findViewById<Button>(R.id.btnDivide).setOnClickListener { onOperatorClick("÷") }

        // Function buttons
        findViewById<Button>(R.id.btnEquals).setOnClickListener { onEqualsClick() }
        findViewById<Button>(R.id.btnClear).setOnClickListener { onClearClick() }
        findViewById<Button>(R.id.btnBackspace).setOnClickListener { onBackspaceClick() }
        findViewById<Button>(R.id.btnDecimal).setOnClickListener { onDecimalClick() }
    }

    private fun onNumberClick(number: String) {
        if (waitingForOperand) {
            currentInput = number
            waitingForOperand = false
        } else {
            currentInput = if (currentInput == "0" || currentInput == getString(R.string.error_message)) {
                number
            } else {
                // Limit input length to prevent overflow
                if (currentInput.length < 15) {
                    currentInput + number
                } else {
                    currentInput
                }
            }
        }
        updateDisplay()
    }

    private fun onOperatorClick(newOperator: String) {
        // Don't allow operator if current input is error
        if (currentInput == getString(R.string.error_message)) {
            return
        }

        val inputValue = currentInput.toDoubleOrNull() ?: return

        if (operator.isNotEmpty() && !waitingForOperand) {
            val result = performCalculation()
            if (result.isNaN() || result.isInfinite()) {
                currentInput = getString(R.string.error_message)
                operator = ""
                waitingForOperand = false
                updateDisplay()
                return
            }
            currentInput = formatResult(result)
            firstOperand = result
        } else {
            firstOperand = inputValue
        }

        operator = newOperator
        waitingForOperand = true
        updateDisplay()
    }

    private fun onEqualsClick() {
        // Don't allow equals if current input is error
        if (currentInput == getString(R.string.error_message)) {
            return
        }

        if (operator.isNotEmpty() && !waitingForOperand) {
            val result = performCalculation()
            currentInput = formatResult(result)
            operator = ""
            waitingForOperand = true
            updateDisplay()
        }
    }

    private fun onClearClick() {
        currentInput = "0"
        operator = ""
        firstOperand = 0.0
        waitingForOperand = false
        updateDisplay()
    }

    private fun onBackspaceClick() {
        // Clear error state with backspace
        if (currentInput == getString(R.string.error_message)) {
            currentInput = "0"
        } else if (currentInput.length > 1) {
            currentInput = currentInput.dropLast(1)
        } else {
            currentInput = "0"
        }
        updateDisplay()
    }

    private fun onDecimalClick() {
        if (currentInput == getString(R.string.error_message)) {
            currentInput = "0."
            waitingForOperand = false
        } else if (waitingForOperand) {
            currentInput = "0."
            waitingForOperand = false
        } else if (!currentInput.contains(".")) {
            currentInput += "."
        }
        updateDisplay()
    }

    private fun onPercentClick() {
        if (currentInput == getString(R.string.error_message)) {
            return
        }

        val value = currentInput.toDoubleOrNull() ?: return
        val result = value / 100.0
        currentInput = formatResult(result)
        waitingForOperand = true
        updateDisplay()
    }

    private fun onPlusMinusClick() {
        if (currentInput == getString(R.string.error_message) || currentInput == "0") {
            return
        }

        val value = currentInput.toDoubleOrNull() ?: return
        val result = -value
        currentInput = formatResult(result)
        updateDisplay()
    }

    private fun performCalculation(): Double {
        val secondOperand = currentInput.toDoubleOrNull() ?: 0.0

        return when (operator) {
            "+" -> firstOperand + secondOperand
            "-" -> firstOperand - secondOperand
            "×" -> firstOperand * secondOperand
            "÷" -> {
                if (secondOperand != 0.0) {
                    firstOperand / secondOperand
                } else {
                    Double.NaN // Division by zero
                }
            }
            else -> secondOperand
        }
    }

    private fun formatResult(result: Double): String {
        return when {
            result.isNaN() -> getString(R.string.error_message)
            result.isInfinite() -> getString(R.string.error_message)
            result == result.toLong().toDouble() -> result.toLong().toString()
            else -> decimalFormat.format(result)
        }
    }

    private fun updateDisplay() {
        display.text = currentInput
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        when (keyCode) {
            KeyEvent.KEYCODE_0 -> onNumberClick("0")
            KeyEvent.KEYCODE_1 -> onNumberClick("1")
            KeyEvent.KEYCODE_2 -> onNumberClick("2")
            KeyEvent.KEYCODE_3 -> onNumberClick("3")
            KeyEvent.KEYCODE_4 -> onNumberClick("4")
            KeyEvent.KEYCODE_5 -> onNumberClick("5")
            KeyEvent.KEYCODE_6 -> onNumberClick("6")
            KeyEvent.KEYCODE_7 -> onNumberClick("7")
            KeyEvent.KEYCODE_8 -> onNumberClick("8")
            KeyEvent.KEYCODE_9 -> onNumberClick("9")
            KeyEvent.KEYCODE_PLUS -> onOperatorClick("+")
            KeyEvent.KEYCODE_MINUS -> onOperatorClick("-")
            KeyEvent.KEYCODE_STAR -> onOperatorClick("×")
            KeyEvent.KEYCODE_SLASH -> onOperatorClick("÷")
            KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_EQUALS -> onEqualsClick()
            KeyEvent.KEYCODE_PERIOD -> onDecimalClick()
            KeyEvent.KEYCODE_DEL, KeyEvent.KEYCODE_FORWARD_DEL -> onBackspaceClick()
            KeyEvent.KEYCODE_C -> onClearClick()
            else -> return super.onKeyDown(keyCode, event)
        }
        return true
    }
}
