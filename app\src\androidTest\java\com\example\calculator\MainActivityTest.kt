package com.example.calculator

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.espresso.matcher.ViewMatchers.withText
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Instrumented test, which will execute on an Android device.
 */
@RunWith(AndroidJUnit4::class)
class MainActivityTest {

    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)

    @Test
    fun testBasicAddition() {
        // Click 2
        onView(withId(R.id.btn2)).perform(click())
        
        // Click +
        onView(withId(R.id.btnPlus)).perform(click())
        
        // Click 3
        onView(withId(R.id.btn3)).perform(click())
        
        // Click =
        onView(withId(R.id.btnEquals)).perform(click())
        
        // Check result is 5
        onView(withId(R.id.tvDisplay)).check(matches(withText("5")))
    }

    @Test
    fun testClearFunction() {
        // Click some numbers
        onView(withId(R.id.btn1)).perform(click())
        onView(withId(R.id.btn2)).perform(click())
        onView(withId(R.id.btn3)).perform(click())
        
        // Click clear
        onView(withId(R.id.btnClear)).perform(click())
        
        // Check display shows 0
        onView(withId(R.id.tvDisplay)).check(matches(withText("0")))
    }

    @Test
    fun testDecimalInput() {
        // Click 1
        onView(withId(R.id.btn1)).perform(click())
        
        // Click decimal
        onView(withId(R.id.btnDecimal)).perform(click())
        
        // Click 5
        onView(withId(R.id.btn5)).perform(click())
        
        // Check display shows 1.5
        onView(withId(R.id.tvDisplay)).check(matches(withText("1.5")))
    }

    @Test
    fun testBackspaceFunction() {
        // Click 1, 2, 3
        onView(withId(R.id.btn1)).perform(click())
        onView(withId(R.id.btn2)).perform(click())
        onView(withId(R.id.btn3)).perform(click())
        
        // Click backspace
        onView(withId(R.id.btnBackspace)).perform(click())
        
        // Check display shows 12
        onView(withId(R.id.tvDisplay)).check(matches(withText("12")))
    }
}
