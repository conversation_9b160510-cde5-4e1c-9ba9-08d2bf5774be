# Android Calculator App

A modern, feature-rich calculator app for Android with a sleek dark theme and intuitive user interface.

## Features

- **Basic Arithmetic Operations**: Addition, subtraction, multiplication, and division
- **Advanced Functions**: 
  - Clear (C) and Backspace (⌫) operations
  - Decimal point support
  - Error handling for division by zero and invalid operations
- **Modern UI**: Dark theme with color-coded buttons
  - Number buttons: Dark gray
  - Operator buttons: Orange
  - Function buttons: Light gray
- **Keyboard Support**: Use physical keyboard for input
- **Responsive Design**: Optimized for various screen sizes

## Technical Details

- **Language**: Kotlin
- **Minimum SDK**: API 24 (Android 7.0)
- **Target SDK**: API 34 (Android 14)
- **Architecture**: Single Activity with View Binding
- **UI Framework**: Material Design Components

## Project Structure

```
app/
├── src/
│   ├── main/
│   │   ├── java/com/example/calculator/
│   │   │   └── MainActivity.kt          # Main calculator logic
│   │   ├── res/
│   │   │   ├── layout/
│   │   │   │   └── activity_main.xml    # Calculator UI layout
│   │   │   ├── drawable/                # Button styles and icons
│   │   │   ├── values/                  # Colors, strings, themes
│   │   │   └── mipmap/                  # App icons
│   │   └── AndroidManifest.xml
│   ├── test/                            # Unit tests
│   └── androidTest/                     # Instrumented tests
└── build.gradle                        # App dependencies
```

## Building and Running

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK with API level 24 or higher
- Kotlin plugin

### Steps
1. Clone or download this project
2. Open the project in Android Studio
3. Wait for Gradle sync to complete
4. Connect an Android device or start an emulator
5. Click "Run" or press Ctrl+R (Cmd+R on Mac)

### Running Tests
- **Unit Tests**: Right-click on `app/src/test` and select "Run Tests"
- **Instrumented Tests**: Right-click on `app/src/androidTest` and select "Run Tests"

## Usage

### Basic Operations
1. Tap number buttons to input values
2. Tap operator buttons (+, -, ×, ÷) to select operations
3. Tap equals (=) to calculate the result
4. Use Clear (C) to reset the calculator
5. Use Backspace (⌫) to delete the last digit

### Keyboard Shortcuts
- **Numbers**: 0-9 keys
- **Operations**: +, -, *, / keys
- **Calculate**: Enter or = key
- **Decimal**: Period (.) key
- **Clear**: C key
- **Backspace**: Delete or Backspace key

## Error Handling

The calculator handles various error conditions:
- Division by zero displays "Error"
- Invalid operations are prevented
- Input length is limited to prevent overflow
- Error state can be cleared with backspace or clear

## Customization

### Colors
Edit `app/src/main/res/values/colors.xml` to customize the color scheme:
- `calculator_background`: Main background color
- `number_button`: Number button color
- `operator_button`: Operator button color
- `function_button`: Function button color

### Strings
Edit `app/src/main/res/values/strings.xml` to change button labels and messages.

## License

This project is open source and available under the MIT License.
